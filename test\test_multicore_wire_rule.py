import pandas as pd
from Eplan_导线.src.wire_count_processor import identify_multicore_wires_from_sleeve

def test_multicore_rule():
    # 构造模拟数据
    data = [
        # 屏柜A，元器件X，端号连续，蓝棕，应该识别为两芯线
        {'屏柜编号': 'A', '导线起点': 'X:1/xxx', '颜色/线径标识': '蓝($)'},
        {'屏柜编号': 'A', '导线起点': 'X:2/xxx', '颜色/线径标识': '棕($)'},
        # 屏柜A，元器件X，端号连续，蓝棕黑，应该识别为四芯线
        {'屏柜编号': 'A', '导线起点': 'X:3/xxx', '颜色/线径标识': '蓝($)'},
        {'屏柜编号': 'A', '导线起点': 'X:4/xxx', '颜色/线径标识': '棕($)'},
        {'屏柜编号': 'A', '导线起点': 'X:5/xxx', '颜色/线径标识': '黑($)'},
        # 屏柜A，元器件X，端号不连续，不能识别为多芯线
        {'屏柜编号': 'A', '导线起点': 'X:7/xxx', '颜色/线径标识': '蓝($)'},
        {'屏柜编号': 'A', '导线起点': 'X:9/xxx', '颜色/线径标识': '棕($)'},
        # 屏柜B，元器件Y，端号连续，蓝棕黑花，应该识别为四芯线
        {'屏柜编号': 'B', '导线起点': 'Y:1/xxx', '颜色/线径标识': '蓝($)'},
        {'屏柜编号': 'B', '导线起点': 'Y:2/xxx', '颜色/线径标识': '棕($)'},
        {'屏柜编号': 'B', '导线起点': 'Y:3/xxx', '颜色/线径标识': '黑($)'},
        {'屏柜编号': 'B', '导线起点': 'Y:4/xxx', '颜色/线径标识': '花($)'},
        # 屏柜B，元器件Y，端号连续但有重复颜色，不应识别为多芯线
        {'屏柜编号': 'B', '导线起点': 'Y:5/xxx', '颜色/线径标识': '蓝($)'},
        {'屏柜编号': 'B', '导线起点': 'Y:6/xxx', '颜色/线径标识': '蓝($)'},
    ]
    df = pd.DataFrame(data)
    multicore_df = identify_multicore_wires_from_sleeve(df)
    print('多芯线明细表:')
    print(multicore_df[['屏柜编号','导线起点','颜色/线径标识','多芯线类型']])

if __name__ == '__main__':
    test_multicore_rule()